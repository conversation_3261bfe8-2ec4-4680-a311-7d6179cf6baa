package com.zerosense.bluetooth

import android.content.Context
import android.util.Log

/**
 * ZeroSense 蓝牙 SDK 入口类
 * 这是客户集成时使用的主要接口
 */
object ZeroSenseBluetoothSDK {

    private const val TAG = "ZeroSenseBluetoothSDK"
    private var bluetoothManager: BluetoothManager? = null

    /**
     * 初始化 SDK
     * @param context 应用上下文
     */
    fun initialize(context: Context) {
        Log.d(TAG, "开始初始化 ZeroSense 蓝牙 SDK...")
        try {
            bluetoothManager = BluetoothManager.getInstance(context)
            Log.d(TAG, "ZeroSense 蓝牙 SDK 初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "ZeroSense 蓝牙 SDK 初始化失败: ${e.message}", e)
            throw e
        }
    }
    
    /**
     * 获取蓝牙管理器实例
     * @return BluetoothManager 实例，如果未初始化则返回 null
     */
    fun getBluetoothManager(): BluetoothManager? {
        return bluetoothManager
    }
    
    /**
     * 检查 SDK 是否已初始化
     */
    fun isInitialized(): Boolean {
        return bluetoothManager != null
    }
    
    /**
     * 释放 SDK 资源
     */
    fun release() {
        bluetoothManager?.release()
        bluetoothManager = null
    }
    
    /**
     * 获取 SDK 版本
     */
    fun getVersion(): String {
        return "1.5.0"
    }
}
