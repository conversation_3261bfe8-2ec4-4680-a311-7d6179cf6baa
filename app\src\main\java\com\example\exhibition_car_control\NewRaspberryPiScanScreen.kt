package com.example.exhibition_car_control

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.zerosense.bluetooth.BluetoothCallback
import com.zerosense.bluetooth.BluetoothDeviceInfo
import com.zerosense.bluetooth.BluetoothManager

/**
 * 扫描设备数据类 - 移到函数外部避免类型解析问题
 */
data class ScanDevice(
    val name: String,
    val address: String,
    val rssi: String,
    val type: String
)

/**
 * 全新的树莓派扫描控制界面
 * 简化架构，直接管理状态，避免复杂的回调链
 */
@Composable
fun NewRaspberryPiScanScreen(
    bluetoothManager: BluetoothManager,
    connectedDevices: List<BluetoothDeviceInfo>
) {
    // 本地状态管理
    var selectedRaspberryPi by remember { mutableStateOf<BluetoothDeviceInfo?>(null) }
    var scanResults by remember { mutableStateOf(emptyList<ScanDevice>()) }
    var isScanning by remember { mutableStateOf(false) }
    var scanStatus by remember { mutableStateOf("准备扫描") }
    var lastMessage by remember { mutableStateOf("") }
    
    // 数据监听器
    DisposableEffect(bluetoothManager) {
        val callback = object : BluetoothCallback {
            override fun onDataReceived(deviceAddress: String, data: String) {
                println("📡 [Dotix扫描界面] 收到数据: '$data'")
                lastMessage = "最新消息: $data"
                
                val cleanData = data.trim()
                
                when {
                    // 开始扫描确认
                    cleanData.contains("A0:01:OK") -> {
                        println("✅ [Dotix扫描界面] 开始扫描确认")
                        isScanning = true
                        scanStatus = "正在扫描..."
                        scanResults = emptyList() // 清空结果
                    }

                    // 停止扫描确认
                    cleanData.contains("A0:00:OK") -> {
                        println("🛑 [Dotix扫描界面] 停止扫描确认")
                        isScanning = false
                        scanStatus = "扫描已停止"
                    }

                    // 扫描结果
                    cleanData.contains("A0:SCAN_RESULT:") -> {
                        println("🔍 [Dotix扫描界面] 处理扫描结果")
                        try {
                            val parts = cleanData.split(":")
                            if (parts.size >= 3) {
                                val deviceInfo = parts[2]
                                val deviceParts = deviceInfo.split("|")
                                if (deviceParts.size >= 3) {
                                    val deviceType = deviceParts[0]
                                    val deviceName = deviceParts[1]
                                    val deviceAddress = deviceParts[2]
                                    val rssi = if (deviceParts.size >= 4) deviceParts[3] else "未知"
                                    
                                    val newDevice = ScanDevice(
                                        name = deviceName,
                                        address = deviceAddress,
                                        rssi = rssi,
                                        type = deviceType
                                    )
                                    
                                    // 检查是否已存在
                                    if (!scanResults.any { device -> device.address == deviceAddress }) {
                                        scanResults = scanResults + newDevice
                                        println("✅ [Dotix扫描界面] 添加设备: ${newDevice.name}, 总数: ${scanResults.size}")
                                        scanStatus = "已找到 ${scanResults.size} 个设备"
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            println("❌ [Dotix扫描界面] 解析失败: ${e.message}")
                        }
                    }

                    // 扫描完成
                    cleanData.contains("A0:SCAN_FINISHED") -> {
                        println("🏁 [Dotix扫描界面] 扫描完成")
                        isScanning = false
                        scanStatus = "扫描完成，共找到 ${scanResults.size} 个设备"
                    }
                }
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {}
            override fun onError(error: String) { 
                lastMessage = "错误: $error"
                isScanning = false
                scanStatus = "扫描出错: $error"
            }
            override fun onSuccess(message: String) { lastMessage = "成功: $message" }
            override fun onInfo(message: String) { lastMessage = "信息: $message" }
            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {}
            override fun onPermissionRequired(permissions: Array<String>) {}
        }
        
        bluetoothManager.addCallback(callback)
        onDispose {
            bluetoothManager.removeCallback(callback)
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔍 Dotix设备扫描控制",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        // 树莓派选择
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "选择Dotix设备",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                val raspberryPiDevices = connectedDevices.filter {
                    it.name?.startsWith("Luban", ignoreCase = true) == true
                }
                
                if (raspberryPiDevices.isEmpty()) {
                    Text(
                        text = "未找到已连接的Dotix设备",
                        color = Color.Gray
                    )
                } else {
                    raspberryPiDevices.forEach { device ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedRaspberryPi?.address == device.address,
                                onClick = { selectedRaspberryPi = device }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = "${device.name} (${device.address})")
                        }
                    }
                }
            }
        }
        
        // 扫描控制
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "扫描控制",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 状态显示
                Text(
                    text = "状态: $scanStatus",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isScanning) Color.Blue else Color.Gray
                )
                
                // 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            selectedRaspberryPi?.let { device ->
                                println("🔍 [Dotix扫描界面] 发送开始扫描指令")
                                bluetoothManager.sendData(device.address, "A0:01")
                                scanStatus = "发送开始扫描指令..."
                            }
                        },
                        enabled = selectedRaspberryPi != null && !isScanning
                    ) {
                        Text("开始扫描")
                    }

                    Button(
                        onClick = {
                            selectedRaspberryPi?.let { device ->
                                println("🛑 [Dotix扫描界面] 发送停止扫描指令")
                                bluetoothManager.sendData(device.address, "A0:00")
                                scanStatus = "发送停止扫描指令..."
                            }
                        },
                        enabled = selectedRaspberryPi != null && isScanning
                    ) {
                        Text("停止扫描")
                    }
                }
            }
        }
        
        // 扫描结果
        Card(
            modifier = Modifier.fillMaxWidth().weight(1f)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "扫描结果 (${scanResults.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (scanResults.isEmpty()) {
                    Text(
                        text = "暂无扫描结果",
                        color = Color.Gray,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(scanResults) { device ->
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(12.dp)
                                ) {
                                    Text(
                                        text = device.name,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = "地址: ${device.address}",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "信号强度: ${device.rssi} dBm",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "类型: ${device.type}",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 调试信息
        if (lastMessage.isNotEmpty()) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                )
            ) {
                Text(
                    text = lastMessage,
                    modifier = Modifier.padding(8.dp),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
