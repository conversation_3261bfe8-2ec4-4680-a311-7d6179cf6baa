package com.zerosense.bluetooth

/**
 * 蓝牙操作回调接口
 */
interface BluetoothCallback {
    
    /**
     * 扫描状态变化回调
     * @param state 扫描状态
     */
    fun onScanStateChanged(state: BluetoothScanState) {}
    
    /**
     * 发现新设备回调
     * @param device 发现的设备信息
     */
    fun onDeviceFound(device: BluetoothDeviceInfo) {}
    
    /**
     * 连接状态变化回调
     * @param device 设备信息
     * @param state 连接状态
     */
    fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {}
    
    /**
     * 错误回调
     * @param error 错误信息
     */
    fun onError(error: String) {}

    /**
     * 成功消息回调
     * @param message 成功消息
     */
    fun onSuccess(message: String) {}

    /**
     * 信息提示回调
     * @param message 信息提示
     */
    fun onInfo(message: String) {}

    /**
     * 权限请求回调
     * @param permissions 需要请求的权限列表
     */
    fun onPermissionRequired(permissions: Array<String>) {}

    /**
     * 数据接收回调
     * @param senderAddress 发送方设备地址
     * @param data 接收到的数据
     */
    fun onDataReceived(senderAddress: String, data: String) {}

    /**
     * BLE特征值通知回调
     * @param deviceAddress 设备地址
     * @param characteristicUuid 特征值UUID
     * @param data 接收到的数据
     */
    fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {}

    /**
     * BLE连接状态变化回调
     * @param deviceAddress 设备地址
     * @param state GATT连接状态
     */
    fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {}
}
