# 📡 蓝牙外设通用通讯协议 

## 📱 **系统架构**

```
手机APP ←蓝牙→ 树莓派 ←蓝牙→ 蓝牙外设(充电器/灯具/其他)
```

**说明**: 树莓派作为蓝牙中继，手机发什么指令，树莓派就转发什么给外设

---

## 📋 **协议核心**

### **基本原理**
- **设备类型**: 告诉我操作什么类型的设备 (充电器/灯具/其他)
- **动作指令**: 具体要做什么操作 (开/关/查询/调节)
- **状态反馈**: 设备当前是什么状态

### **通用BLE服务结构**
所有外设都使用相同的BLE服务结构：

| 项目 | UUID | 属性 | 说明 |
|------|------|------|------|
| **主服务** | `0000FFE0-0000-1000-8000-00805F9B34FB` | - | 通用外设主服务 |
| **控制特征值** | `0000FFE1-0000-1000-8000-00805F9B34FB` | WRITE | 发送控制指令到外设 |
| **状态特征值** | `0000FFE2-0000-1000-8000-00805F9B34FB` | READ, NOTIFY | 外设状态信息 |
| **按键通知** | `0000FFE4-0000-1000-8000-00805F9B34FB` | NOTIFY | 按键设备专用通知 |
| **设备信息** | `0000FFE5-0000-1000-8000-00805F9B34FB` | READ | 设备型号、版本等信息 |


### **设备定义**
| 设备类型 | 类型 | 名称 | 说明 |
|----------|------|------|------|
| 点云灵动键 | `01` | Dotix B1-XXX | 按键设备（已占用，仅用于按键通知） |
| 无线充电器 | `02` | SMART-WC | 蓝牙无线充电设备 |
| Lamp主灯 | `03` | SMART-ML | 主要照明灯具 |
| 氛围灯 | `04` | SMART-AL | 装饰性彩色灯具 |
| 香氛机 | `05` | SMART-DF | 香氛扩散设备 |
| 风扇 | `06` | SMART-FN | 电风扇设备 |

### **协议数据格式**
```
控制指令 (写入0xFFE1): [设备类型][指令码][数据...]
状态反馈 (读取0xFFE2或通知): [设备类型][状态码][数值]
按键通知 (0xFFE4通知): [按键ID][动作类型][其他数据...]
设备信息 (读取0xFFE5): [设备型号][版本号][其他信息...]
```

**说明**:
- **0xFFE1**: 用于主动控制外设（充电器、灯具等）
- **0xFFE2**: 用于获取外设状态反馈
- **0xFFE4**: 专用于按键设备的按键通知
- **0xFFE5**: 用于读取设备基本信息

---

## 📋 **支持的BLE外设类型**

### 🔌 **无线充电器 (设备类型: 02)**
| 功能 | 控制指令 (0xFFE1) | 状态反馈 (0xFFE2) | 说明 |
|------|-------------------|-------------------|------|
| 开启充电 | `02 01` | `02 01` = 充电中 | 启动无线充电 |
| 关闭充电 | `02 00` | `02 00` = 待机 | 停止无线充电 |
| 查询状态 | `02 02` | `02 FF` = 异常 | 请求当前状态 |

### 💡 **Lamp主灯 (设备类型: 03)**
| 功能 | 控制指令 (0xFFE1) | 状态反馈 (0xFFE2) | 说明 |
|------|-------------------|-------------------|------|
| 开灯 | `03 01` | `03 01` = 开启 | 打开主灯 |
| 关灯 | `03 00` | `03 00` = 关闭 | 关闭主灯 |
| 查询状态 | `03 02` | `03 FF` = 异常 | 请求当前状态 |

### 🌈 **氛围灯 (设备类型: 04)**
| 功能 | 控制指令 (0xFFE1) | 状态反馈 (0xFFE2) | 说明 |
|------|-------------------|-------------------|------|
| 开启 | `04 01` | `04 01` = 开启 | 打开氛围灯 |
| 关闭 | `04 00` | `04 00` = 关闭 | 关闭氛围灯 |
| 查询状态 | `04 02` | `04 FF` = 异常 | 请求当前状态 |

### 🌸 **香氛机 (设备类型: 05)**
| 功能 | 控制指令 (0xFFE1) | 状态反馈 (0xFFE2) | 说明 |
|------|-------------------|-------------------|------|
| 开启 | `05 01` | `05 01` = 开启 | 启动香氛 |
| 关闭 | `05 00` | `05 00` = 关闭 | 停止香氛 |
| 查询状态 | `05 02` | `05 FF` = 异常 | 请求当前状态 |

### 🌀 **风扇 (设备类型: 06)**
| 功能 | 控制指令 (0xFFE1) | 状态反馈 (0xFFE2) | 说明 |
|------|-------------------|-------------------|------|
| 开启 | `06 01` | `06 01` = 开启 | 启动风扇 |
| 关闭 | `06 00` | `06 00` = 关闭 | 停止风扇 |
| 查询状态 | `06 02` | `06 FF` = 异常 | 请求当前状态 |

---

