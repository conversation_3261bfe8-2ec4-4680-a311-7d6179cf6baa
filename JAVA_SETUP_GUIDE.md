# Java 11 安装和配置指南

## 🎯 问题描述
当前系统使用 Java 8，但 Android Gradle Plugin 7.0+ 需要 Java 11 或更高版本。

## 📥 解决方案 1：安装 Java 11

### 1. 下载 Java 11
推荐使用 OpenJDK 11：
- **Eclipse Temurin**: https://adoptium.net/temurin/releases/?version=11
- **Oracle JDK 11**: https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html

### 2. 安装步骤
1. 下载 Windows x64 版本的 JDK 11
2. 运行安装程序，安装到默认位置（通常是 `C:\Program Files\Eclipse Adoptium\jdk-11.x.x.x-hotspot\`）
3. 记住安装路径

### 3. 配置 gradle.properties
更新项目的 `gradle.properties` 文件：

```properties
# 指定 Gradle 使用 Java 11
org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot
```

### 4. 在 Android Studio 中配置
1. 打开 Android Studio
2. 进入 `File` → `Settings`
3. 导航到 `Build, Execution, Deployment` → `Build Tools` → `Gradle`
4. 设置 `Gradle JDK` 为刚安装的 Java 11
5. 点击 `Apply` 和 `OK`
6. 同步项目

## 🔄 解决方案 2：恢复兼容配置

如果不想安装 Java 11，可以使用与 Java 8 兼容的版本：

### 更新版本配置
```toml
# gradle/libs.versions.toml
[versions]
agp = "7.3.1"
kotlin = "1.8.10"
composeBom = "2023.06.01"
```

```properties
# gradle/wrapper/gradle-wrapper.properties
distributionUrl=https\://services.gradle.org/distributions/gradle-7.6.1-bin.zip
```

```properties
# gradle.properties
# 强制使用 Java 8
org.gradle.java.home=C:\\Program Files\\Java\\jdk1.8.0_112
```

然后在 Android Studio 中：
1. 设置 `Gradle JDK` 为 Java 11 或 Java 17（不要选择 Java 21）
2. 让 Android Studio 使用较新的 Java 版本，但项目构建使用 Java 8

## ✅ 推荐方案
**推荐使用解决方案 1**，因为：
- Java 11 是长期支持版本
- 与现代 Android 开发工具兼容性更好
- 避免了复杂的版本兼容性问题

## 🚀 验证安装
安装完成后，运行以下命令验证：

```bash
./gradlew assembleDebug
```

如果成功构建，说明配置正确。
