# Consumer ProGuard rules for ZeroSense Bluetooth SDK

# Keep all public API classes and methods
-keep public class com.zerosense.bluetooth.ZeroSenseBluetoothSDK { *; }
-keep public class com.zerosense.bluetooth.BluetoothManager { *; }
-keep public class com.zerosense.bluetooth.BluetoothDeviceInfo { *; }
-keep public class com.zerosense.bluetooth.BluetoothCallback { *; }
-keep public enum com.zerosense.bluetooth.BluetoothConnectionState { *; }
-keep public enum com.zerosense.bluetooth.BluetoothScanState { *; }

# Keep Parcelable implementation
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
