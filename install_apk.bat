@echo off
echo ========================================
echo 蓝牙 SDK 演示应用安装脚本
echo ========================================
echo.

REM 检查 ADB 是否可用
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 ADB 工具
    echo 请确保 Android SDK 已安装并且 ADB 在 PATH 中
    echo.
    pause
    exit /b 1
)

REM 检查设备连接
echo 检查连接的设备...
adb devices
echo.

REM 检查 APK 文件是否存在
if not exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo 错误: 未找到 APK 文件
    echo 请先运行构建命令: gradlew assembleDebug
    echo.
    pause
    exit /b 1
)

echo 准备安装 APK...
echo 文件: app\build\outputs\apk\debug\app-debug.apk
echo.

REM 安装 APK
echo 正在安装...
adb install -r "app\build\outputs\apk\debug\app-debug.apk"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 安装成功！
    echo ========================================
    echo.
    echo 应用已安装到您的设备上
    echo 应用名称: Exhibition Car Control
    echo 包名: com.example.exhibition_car_control
    echo.
    echo 您现在可以在设备上启动应用进行测试
) else (
    echo.
    echo ========================================
    echo 安装失败！
    echo ========================================
    echo.
    echo 可能的原因:
    echo 1. 设备未连接或未授权
    echo 2. 设备上已安装了不兼容的版本
    echo 3. 设备存储空间不足
    echo.
    echo 请检查设备连接状态并重试
)

echo.
pause
