# 📱 手机控制树莓派蓝牙设备方案

## 🎯 需求概述

实现手机通过蓝牙控制树莓派的蓝牙扫描、连接、配对功能，并能查看和控制树莓派连接的外设设备。

## 🏗️ 系统架构

```
手机APP ←蓝牙SPP→ 树莓派 ←蓝牙BLE→ 外设(充电器/灯具等)
```

**通讯流程**：
1. 手机通过经典蓝牙SPP协议连接树莓派
2. 手机发送控制指令给树莓派
3. 树莓派执行蓝牙操作（扫描、连接外设等）
4. 树莓派通过BLE协议控制外设
5. 树莓派将结果反馈给手机

## 📋 通讯协议设计

### 指令格式
```
发送指令: [指令类型]:[操作码]:[参数...]
响应格式: [指令类型]:[状态码]:[数据...]
```

### 指令类型定义

#### 1. 树莓派蓝牙管理指令 (A0)
- `A0:01` - 开始BLE扫描
- `A0:00` - 停止BLE扫描

#### 2. 外设控制指令 (A1)
- `A1:[设备类型]:[指令]:[设备地址]` - 控制外设
- 示例: `A1:02:01:AA:BB:CC:DD:EE:FF` (开启充电器)

#### 3. 状态查询指令 (A2)
- `A2:00` - 查询所有已连接设备

### 响应格式

#### 设备列表响应
```
A2:00:[设备类型|设备名|设备地址];[设备类型|设备名|设备地址]...
```

#### 控制结果响应
```
A1:[设备类型]:[指令]:[设备地址]:OK/FAIL
```

## 🔧 实现方案（最小改动）

### 1. 手机端改动

#### 配置文件修改
在 `DeviceConfig.kt` 中新增功能开关：
```kotlin
val showRaspberryPiControl: Boolean = isPhoneVersion  // 显示树莓派控制功能
```

#### 新增UI组件
- `RaspberryPiControlSection` - 树莓派控制界面
- 包含设备选择、扫描控制、外设管理等功能

#### 响应处理
- `processRaspberryPiResponse()` - 解析树莓派响应
- 更新设备列表和状态

### 2. 树莓派端改动

#### 指令处理
在 `BluetoothManager.kt` 中新增：
- `processRaspberryPiCommand()` - 解析和执行控制指令
- `sendResponse()` - 发送响应给手机
- 支持BLE扫描、设备控制、状态查询

#### BLE设备管理
- 利用现有的 `BleManager` 进行设备操作
- 自动维护已连接设备列表
- 转发外设控制指令

## 📱 用户操作流程

### 1. 连接树莓派
1. 手机扫描并连接到树莓派设备
2. 在"树莓派控制中心"选择目标树莓派

### 2. 管理外设
1. 点击"开始扫描" - 树莓派开始扫描BLE设备
2. 点击"刷新设备" - 查看已连接的外设列表
3. 对每个外设进行开关控制

### 3. 查看状态
- 实时显示外设的开关状态
- 不同设备类型显示对应图标
- 状态变化实时反馈

## 🎨 UI设计

### 树莓派控制界面
```
🍓 树莓派控制中心
├── 选择树莓派设备 (FilterChip列表)
├── 蓝牙设备管理
│   ├── [开始扫描] [停止扫描] [刷新设备]
└── 已连接的外设
    ├── 🔌 无线充电器 [●开启] [关闭]
    ├── 💡 Lamp主灯    [○关闭] [开启]
    └── 🌈 氛围灯      [●开启] [关闭]
```

## 🔄 设备类型映射

| 设备类型 | 代码 | 图标 | 说明 |
|----------|------|------|------|
| 无线充电器 | 02 | 🔌 | 蓝牙无线充电设备 |
| Lamp主灯 | 03 | 💡 | 主要照明灯具 |
| 氛围灯 | 04 | 🌈 | 装饰性彩色灯具 |
| 香氛机 | 05 | 🌸 | 香氛扩散设备 |
| 风扇 | 06 | 🌀 | 电风扇设备 |

## ✅ 优势特点

### 1. 最小改动
- 复用现有蓝牙通讯框架
- 利用现有BLE管理功能
- 只新增必要的UI和指令处理

### 2. 协议简单
- 基于文本的简单协议
- 易于调试和扩展
- 向后兼容现有功能

### 3. 用户友好
- 直观的图形界面
- 实时状态反馈
- 一键式操作

### 4. 可扩展性
- 易于添加新的设备类型
- 支持更多控制指令
- 可扩展更复杂的状态查询

## 🚀 部署说明

### 手机版配置
```kotlin
private val DEVICE_TYPE = DeviceType.PHONE
```

### 树莓派版配置
```kotlin
private val DEVICE_TYPE = DeviceType.RASPBERRY_PI
```

部署后，手机版会显示"树莓派控制中心"，树莓派版会自动启动蓝牙服务器等待连接。

## 📝 注意事项

1. **权限要求**: 确保蓝牙权限已正确配置
2. **设备配对**: 手机和树莓派需要先完成蓝牙配对
3. **网络稳定**: 保持蓝牙连接稳定性
4. **错误处理**: 实现了基本的错误处理和重试机制
5. **状态同步**: 设备状态通过查询指令保持同步
