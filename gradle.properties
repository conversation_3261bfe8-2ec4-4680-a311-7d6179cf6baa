# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# JVM 参数设置
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseG1GC

# 指定 Gradle 使用的 Java 版本（如果系统有 Java 11）
# org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.5

# 指定 Gradle 使用 JDK 11（请根据实际安装路径调整）
# 常见的 JDK 11 安装路径，请取消注释正确的路径：
# org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.21
# org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot
# org.gradle.java.home=C:\\Program Files\\OpenJDK\\jdk-11.0.2
# 指定 Gradle 使用 JDK 11.0.5
org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.5

# Android 构建设置
android.defaults.buildfeatures.buildconfig=true
android.nonFinalResIds=false