# 蓝牙 SDK 安装和使用指南

## 构建成功！

恭喜！蓝牙 SDK 项目已经成功构建完成。

## 生成的文件

### 1. 演示应用 APK
- **文件位置**: `app/build/outputs/apk/debug/app-debug.apk`
- **文件大小**: 约 7.5MB
- **生成时间**: 2025/7/30 2:34:17

### 2. 蓝牙 SDK AAR
- **文件位置**: `bluetooth-sdk/build/outputs/aar/bluetooth-sdk-debug.aar`
- **文件大小**: 约 25KB
- **生成时间**: 2025/7/30 2:34:09

## 安装演示应用到手机

### 方法一：使用 ADB 安装
```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 方法二：直接传输安装
1. 将 `app-debug.apk` 文件传输到手机
2. 在手机上启用"未知来源"安装
3. 点击 APK 文件进行安装

## 使用 SDK

### 集成到您的项目

1. **复制 AAR 文件**
   ```
   将 bluetooth-sdk-debug.aar 复制到您项目的 libs 目录
   ```

2. **在 build.gradle 中添加依赖**
   ```kotlin
   dependencies {
       implementation files('libs/bluetooth-sdk-debug.aar')
       
       // 必需的依赖项
       implementation 'androidx.core:core-ktx:1.9.0'
       implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
   }
   ```

3. **添加权限到 AndroidManifest.xml**
   ```xml
   <!-- 蓝牙权限 -->
   <uses-permission android:name="android.permission.BLUETOOTH" />
   <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
   <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
   
   <!-- Android 12+ 权限 -->
   <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
   <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
   
   <!-- 蓝牙功能声明 -->
   <uses-feature android:name="android.hardware.bluetooth" android:required="true" />
   ```

### 基本使用示例

```kotlin
import com.zerosense.bluetooth.*

class MainActivity : AppCompatActivity() {
    private lateinit var bluetoothManager: BluetoothManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化 SDK
        ZeroSenseBluetoothSDK.initialize(this)
        bluetoothManager = ZeroSenseBluetoothSDK.getBluetoothManager()
        
        // 设置回调
        bluetoothManager.addCallback(object : BluetoothCallback {
            override fun onScanStateChanged(state: BluetoothScanState) {
                // 处理扫描状态变化
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                // 处理发现的设备
            }
            
            override fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
                // 处理连接状态变化
            }
            
            override fun onError(error: String) {
                // 处理错误
            }
            
            override fun onPermissionRequired(permissions: Array<String>) {
                // 处理权限请求
            }
        })
        
        // 开始扫描
        bluetoothManager.startScan()
    }
}
```

## SDK 功能

### 主要功能
1. **设备扫描**: 扫描附近的蓝牙设备
2. **设备连接**: 连接到指定的蓝牙设备
3. **静默连接**: 自动连接到已配对的设备
4. **状态管理**: 实时监控蓝牙和连接状态
5. **权限处理**: 自动处理蓝牙相关权限

### API 接口
- `ZeroSenseBluetoothSDK.initialize(context)` - 初始化 SDK
- `ZeroSenseBluetoothSDK.getBluetoothManager()` - 获取蓝牙管理器
- `bluetoothManager.startScan()` - 开始扫描
- `bluetoothManager.stopScan()` - 停止扫描
- `bluetoothManager.connectToDevice(device)` - 连接设备
- `bluetoothManager.disconnectFromDevice(device)` - 断开设备
- `bluetoothManager.getPairedDevices()` - 获取已配对设备
- `bluetoothManager.getConnectedDevices()` - 获取已连接设备

## 测试应用

安装完成后，您可以：
1. 启动应用测试蓝牙扫描功能
2. 查看已配对的设备列表
3. 尝试连接到蓝牙设备
4. 测试静默连接功能

## 注意事项

1. **权限**: 确保应用已获得所有必要的蓝牙权限
2. **位置权限**: Android 6.0+ 需要位置权限才能扫描蓝牙设备
3. **蓝牙状态**: 确保设备蓝牙已启用
4. **目标设备**: 确保目标设备处于可发现状态

## 故障排除

如果遇到问题：
1. 检查权限是否已正确授予
2. 确认蓝牙已启用
3. 检查目标设备是否可发现
4. 查看应用日志获取详细错误信息

---

**构建时间**: 2025年7月30日
**SDK 版本**: 1.0.0-debug
**最低 Android 版本**: API 21 (Android 5.0)
**目标 Android 版本**: API 34 (Android 14)
