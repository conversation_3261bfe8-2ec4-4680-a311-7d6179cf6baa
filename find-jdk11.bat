@echo off
echo 正在查找 JDK 11 安装路径...
echo.

echo 检查常见的 JDK 11 安装位置：
echo.

if exist "C:\Program Files\Java\jdk-11*" (
    echo 找到 Oracle JDK 11:
    dir "C:\Program Files\Java\jdk-11*" /AD /B
    echo.
)

if exist "C:\Program Files\Eclipse Adoptium\jdk-11*" (
    echo 找到 Eclipse Temurin JDK 11:
    dir "C:\Program Files\Eclipse Adoptium\jdk-11*" /AD /B
    echo.
)

if exist "C:\Program Files\OpenJDK\jdk-11*" (
    echo 找到 OpenJDK 11:
    dir "C:\Program Files\OpenJDK\jdk-11*" /AD /B
    echo.
)

if exist "C:\Program Files (x86)\Java\jdk-11*" (
    echo 找到 32位 JDK 11:
    dir "C:\Program Files (x86)\Java\jdk-11*" /AD /B
    echo.
)

echo 检查 JAVA_HOME 环境变量:
echo JAVA_HOME = %JAVA_HOME%
echo.

echo 检查当前 Java 版本:
java -version
echo.

echo 请根据上述信息，在 gradle.properties 中设置正确的路径
echo 例如：org.gradle.java.home=C:\\Program Files\\Java\\jdk-11.0.21
pause
