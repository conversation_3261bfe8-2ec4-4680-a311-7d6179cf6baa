@echo off
chcp 65001 > nul

echo Building ZeroSense Bluetooth SDK...
echo.

echo 1. Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo Clean failed!
    pause
    exit /b 1
)

echo.
echo 2. Building SDK AAR file...
call gradlew :bluetooth-sdk:assembleRelease
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo 3. Copying AAR file to output directory...
if not exist "output" mkdir "output"

set sourceFile=bluetooth-sdk\build\outputs\aar\bluetooth-sdk-release.aar
set targetFile=output\zerosense-bluetooth-sdk-1.0.0.aar

if exist "%sourceFile%" (
    copy "%sourceFile%" "%targetFile%" > nul
    echo AAR file copied successfully!
) else (
    echo AAR file not found: %sourceFile%
    pause
    exit /b 1
)

echo.
echo 4. Copying documentation...
copy "README.md" "output\README.md" > nul

echo.
echo SDK build completed!
echo Output files location:
echo - output\zerosense-bluetooth-sdk-1.0.0.aar
echo - output\README.md
echo.

pause
