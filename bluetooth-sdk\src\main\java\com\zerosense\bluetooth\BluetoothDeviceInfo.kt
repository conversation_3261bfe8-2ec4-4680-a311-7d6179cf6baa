package com.zerosense.bluetooth

import android.bluetooth.BluetoothDevice
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 蓝牙设备信息数据类
 */
@Parcelize
data class BluetoothDeviceInfo(
    val name: String?,
    val address: String,
    val rssi: Int = 0,
    val isConnected: Boolean = false,
    val isPaired: Boolean = false
) : Parcelable {
    
    companion object {
        /**
         * 从 BluetoothDevice 创建 BluetoothDeviceInfo
         */
        fun fromBluetoothDevice(device: BluetoothDevice, rssi: Int = 0): BluetoothDeviceInfo {
            return BluetoothDeviceInfo(
                name = device.name,
                address = device.address,
                rssi = rssi,
                isConnected = false,
                isPaired = device.bondState == BluetoothDevice.BOND_BONDED
            )
        }
    }
    
    /**
     * 获取显示名称，如果设备名为空则显示地址
     */
    fun getDisplayName(): String {
        return name?.takeIf { it.isNotBlank() } ?: address
    }
}
