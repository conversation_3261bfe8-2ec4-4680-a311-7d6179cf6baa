package com.example.exhibition_car_control

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.zerosense.bluetooth.*
import com.example.exhibition_car_control.config.DeviceConfig

@Composable
fun BluetoothControlSection(
    bluetoothManager: BluetoothManager?,
    scanState: BluetoothScanState,
    discoveredDevices: List<BluetoothDeviceInfo>,
    pairedDevices: List<BluetoothDeviceInfo>,
    onRefreshPairedDevices: () -> Unit,
    deviceConnectionStates: Map<String, BluetoothConnectionState> = emptyMap()
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = if (DeviceConfig.isPhoneVersion) "蓝牙客户端（发送方）" else "蓝牙客户端（接收方）",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = Color(DeviceConfig.UI.primaryColor)
        )

        // 扫描控制区域（用于扫描BLE按键设备）
        if (DeviceConfig.Features.showDeviceScanning) {
            ScanControlCard(
                bluetoothManager = bluetoothManager,
                scanState = scanState
            )
        }

        // 已发现设备（扫描到的BLE按键设备）
        if (DeviceConfig.Features.showDeviceScanning) {
            DeviceListCard(
                title = "已发现设备",
                devices = discoveredDevices,
                bluetoothManager = bluetoothManager,
                showConnectButton = true,
                deviceConnectionStates = deviceConnectionStates
            )
        }

        // 已配对设备
        DeviceListCard(
            title = if (DeviceConfig.Features.showDataSending) "已配对设备（可发送数据）" else "已配对设备（BLE按键设备）",
            devices = pairedDevices,
            bluetoothManager = bluetoothManager,
            showSendDataButton = DeviceConfig.Features.showDataSending,
            onRefresh = onRefreshPairedDevices,
            deviceConnectionStates = deviceConnectionStates
        )

        // 静默连接控制（只在树莓派版显示）
        if (DeviceConfig.Features.showSilentConnection) {
            SilentConnectionCard(bluetoothManager = bluetoothManager)
        }
    }
}

@Composable
fun ScanControlCard(
    bluetoothManager: BluetoothManager?,
    scanState: BluetoothScanState
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "蓝牙扫描",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = { bluetoothManager?.startScan() },
                    enabled = scanState != BluetoothScanState.SCANNING
                ) {
                    Text("开始扫描")
                }
                
                Button(
                    onClick = { bluetoothManager?.stopScan() },
                    enabled = scanState == BluetoothScanState.SCANNING
                ) {
                    Text("停止扫描")
                }
                
                // 扫描状态指示器
                when (scanState) {
                    BluetoothScanState.SCANNING -> {
                        CircularProgressIndicator(modifier = Modifier.size(24.dp))
                        Text("扫描中...")
                    }
                    BluetoothScanState.SCAN_FINISHED -> {
                        Text("扫描完成", color = Color.Green)
                    }
                    BluetoothScanState.SCAN_FAILED -> {
                        Text("扫描失败", color = Color.Red)
                    }
                    else -> {
                        Text("未开始")
                    }
                }
            }
        }
    }
}

@Composable
fun DeviceListCard(
    title: String,
    devices: List<BluetoothDeviceInfo>,
    bluetoothManager: BluetoothManager?,
    showConnectButton: Boolean = false,
    showDisconnectButton: Boolean = false,
    showSilentConnectButton: Boolean = false,
    showSendDataButton: Boolean = false,
    onRefresh: (() -> Unit)? = null,
    deviceConnectionStates: Map<String, BluetoothConnectionState> = emptyMap()
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "$title (${devices.size})",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                onRefresh?.let {
                    TextButton(onClick = it) {
                        Text("刷新")
                    }
                }
            }
            
            if (devices.isEmpty()) {
                Text(
                    text = "暂无设备",
                    color = Color.Gray,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            } else {
                for (device in devices) {
                    DeviceItem(
                        device = device,
                        bluetoothManager = bluetoothManager,
                        showConnectButton = showConnectButton,
                        showDisconnectButton = showDisconnectButton,
                        showSilentConnectButton = showSilentConnectButton,
                        showSendDataButton = showSendDataButton,
                        connectionState = deviceConnectionStates[device.address]
                    )
                }
            }
        }
    }
}

@Composable
fun DeviceItem(
    device: BluetoothDeviceInfo,
    bluetoothManager: BluetoothManager?,
    showConnectButton: Boolean = false,
    showDisconnectButton: Boolean = false,
    showSilentConnectButton: Boolean = false,
    showSendDataButton: Boolean = false,
    connectionState: BluetoothConnectionState? = null
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (device.isConnected) Color.Green.copy(alpha = 0.1f) 
                           else MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column {
                    Text(
                        text = device.getDisplayName(),
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = device.address,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    if (device.rssi != 0) {
                        Text(
                            text = "信号强度: ${device.rssi} dBm",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }
                
                Column(
                    horizontalAlignment = Alignment.End,
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    if (device.isPaired) {
                        Text(
                            text = "已配对",
                            fontSize = 12.sp,
                            color = Color(0xFF006400) // 深绿色，更清晰可见
                        )
                    }
                    if (device.isConnected) {
                        Text(
                            text = "已连接",
                            fontSize = 12.sp,
                            color = Color(0xFF228B22) // 森林绿，更清晰可见
                        )
                    }
                }
            }
            
            // 操作按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (showConnectButton) {
                    // 检查是否正在连接
                    val isConnecting = connectionState == BluetoothConnectionState.CONNECTING

                    if (isConnecting) {
                        // 显示取消按钮
                        Button(
                            onClick = { bluetoothManager?.cancelConnection(device.address) },
                            modifier = Modifier.height(32.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF9800))
                        ) {
                            Text("取消", fontSize = 12.sp)
                        }
                    } else if (device.isPaired) {
                        // 已配对设备显示发送数据按钮
                        var showSendDialog by remember { mutableStateOf(false) }

                        Button(
                            onClick = { showSendDialog = true },
                            modifier = Modifier.height(32.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Green)
                        ) {
                            Text("发送数据", fontSize = 12.sp)
                        }

                        if (showSendDialog) {
                            SendDataDialog(
                                deviceName = device.getDisplayName(),
                                onSend = { data ->
                                    bluetoothManager?.sendData(device.address, data)
                                    showSendDialog = false
                                },
                                onDismiss = { showSendDialog = false }
                            )
                        }
                    } else {
                        // 未配对设备显示配对按钮
                        Button(
                            onClick = { bluetoothManager?.connectToDevice(device.address) },
                            modifier = Modifier.height(32.dp)
                        ) {
                            Text("配对", fontSize = 12.sp)
                        }
                    }
                }

                if (showSendDataButton && device.isPaired) {
                    var showSendDialog by remember { mutableStateOf(false) }

                    Button(
                        onClick = { showSendDialog = true },
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1976D2)) // 深蓝色，更清晰可见
                    ) {
                        Text("发送数据", fontSize = 12.sp, color = Color.White)
                    }

                    if (showSendDialog) {
                        SendDataDialog(
                            deviceName = device.getDisplayName(),
                            onSend = { data ->
                                bluetoothManager?.sendData(device.address, data)
                                showSendDialog = false
                            },
                            onDismiss = { showSendDialog = false }
                        )
                    }
                }
                
                if (showDisconnectButton && device.isConnected) {
                    Button(
                        onClick = { bluetoothManager?.disconnectFromDevice(device.address) },
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                    ) {
                        Text("断开", fontSize = 12.sp)
                    }
                }
            }
        }
    }
}

@Composable
fun SilentConnectionCard(bluetoothManager: BluetoothManager?) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "静默连接控制",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "自动连接所有已配对的设备，无需用户干预",
                fontSize = 14.sp,
                color = Color.Gray
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    onClick = { bluetoothManager?.connectToPairedDevicesAutomatically() }
                ) {
                    Text("连接所有已配对设备")
                }
                
                Button(
                    onClick = { bluetoothManager?.disconnectAll() },
                    colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                ) {
                    Text("断开所有连接")
                }
            }
        }
    }
}

@Composable
fun SendDataDialog(
    deviceName: String,
    onSend: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var inputText by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "发送数据到 $deviceName",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                OutlinedTextField(
                    value = inputText,
                    onValueChange = { inputText = it },
                    label = { Text("输入要发送的数据") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("例如: Hello World") }
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 预设按钮
                    Button(
                        onClick = { inputText = "Hello" },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Hello", fontSize = 12.sp)
                    }

                    Button(
                        onClick = { inputText = "Test Message" },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("测试", fontSize = 12.sp)
                    }

                    Button(
                        onClick = { inputText = "LED_ON" },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("LED开", fontSize = 12.sp)
                    }

                    Button(
                        onClick = { inputText = "LED_OFF" },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("LED关", fontSize = 12.sp)
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            if (inputText.isNotBlank()) {
                                onSend(inputText)
                            }
                        },
                        enabled = inputText.isNotBlank()
                    ) {
                        Text("发送")
                    }
                }
            }
        }
    }
}
