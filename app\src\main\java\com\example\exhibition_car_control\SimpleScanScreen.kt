package com.example.exhibition_car_control

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zerosense.bluetooth.*
import com.example.exhibition_car_control.config.DeviceConfig

/**
 * 简化的扫描界面 - 正确的流程实现
 * 
 * 流程：
 * 1. 手机发送扫描指令给树莓派
 * 2. 手机显示"正在扫描..."状态（无倒计时）
 * 3. 树莓派控制扫描时长并执行扫描
 * 4. 树莓派扫描完成后发送完整设备列表
 * 5. 手机接收并显示扫描结果
 * 6. 用户可点击设备进行连接
 */
@Composable
fun SimpleScanScreen(
    bluetoothManager: BluetoothManager,
    connectedDevices: List<BluetoothDeviceInfo>
) {
    // 状态管理
    var selectedRaspberryPi by remember { mutableStateOf<BluetoothDeviceInfo?>(null) }
    var isScanning by remember { mutableStateOf(false) }
    var scanStatus by remember { mutableStateOf("准备扫描") }
    var scannedDevices by remember { mutableStateOf<List<ScannedDevice>>(emptyList()) }
    var lastMessage by remember { mutableStateOf("") }
    
    // 数据接收监听
    DisposableEffect(bluetoothManager) {
        val callback = object : BluetoothCallback {
            override fun onDataReceived(deviceAddress: String, data: String) {
                println("📡 [SimpleScan] 收到数据: '$data'")
                lastMessage = "收到: $data"
                
                // 处理扫描响应
                when {
                    data == "SCAN_STARTED" -> {
                        isScanning = true
                        scanStatus = "正在扫描..."
                        scannedDevices = emptyList()
                    }
                    data == "SCAN_STOPPED" -> {
                        isScanning = false
                        scanStatus = "扫描已停止"
                    }
                    data == "SCAN_ALREADY_RUNNING" -> {
                        scanStatus = "扫描已在进行中"
                    }
                    data == "SCAN_FAILED" -> {
                        isScanning = false
                        scanStatus = "扫描启动失败"
                    }
                    data.startsWith("SCAN_COMPLETE:") -> {
                        isScanning = false
                        val deviceListStr = data.removePrefix("SCAN_COMPLETE:")
                        if (deviceListStr.isNotEmpty()) {
                            val devices = parseDeviceList(deviceListStr)
                            scannedDevices = devices
                            scanStatus = "扫描完成，找到 ${devices.size} 个设备"
                        } else {
                            scannedDevices = emptyList()
                            scanStatus = "扫描完成，未找到设备"
                        }
                    }
                    data.startsWith("CONNECT_RESULT:") -> {
                        val parts = data.split(":")
                        if (parts.size >= 3) {
                            val result = parts[1]
                            val address = parts[2]
                            scanStatus = if (result == "SUCCESS") {
                                "设备 $address 连接成功"
                            } else {
                                "设备 $address 连接失败"
                            }
                        }
                    }
                }
            }
            
            override fun onError(error: String) {
                scanStatus = "错误: $error"
                isScanning = false
            }
            
            override fun onInfo(info: String) {
                lastMessage = info
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {}
            override fun onScanStateChanged(state: BluetoothScanState) {}
            override fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {}
            override fun onPermissionRequired(permissions: Array<String>) {}
        }
        
        bluetoothManager.addCallback(callback)
        onDispose {
            bluetoothManager.removeCallback(callback)
        }
    }
    
    // 自动选择第一个Luban设备
    LaunchedEffect(connectedDevices) {
        if (selectedRaspberryPi == null) {
            selectedRaspberryPi = connectedDevices.firstOrNull { 
                it.name?.contains("Luban") == true 
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔍 蓝牙设备扫描",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(DeviceConfig.UI.primaryColor)
        )
        
        // 树莓派选择
        RaspberryPiSelector(
            connectedDevices = connectedDevices,
            selectedDevice = selectedRaspberryPi,
            onDeviceSelected = { selectedRaspberryPi = it }
        )
        
        // 扫描控制
        ScanControlSection(
            selectedRaspberryPi = selectedRaspberryPi,
            isScanning = isScanning,
            scanStatus = scanStatus,
            onStartScan = {
                if (selectedRaspberryPi != null && !isScanning) {
                    scanStatus = "发送扫描指令..."
                    scannedDevices = emptyList() // 清空之前的结果

                    // 发送扫描指令
                    val command = "SCAN_START"
                    bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                    println("🔍 发送扫描指令: $command")
                } else if (isScanning) {
                    scanStatus = "扫描正在进行中，请等待..."
                } else {
                    scanStatus = "请先选择树莓派设备"
                }
            },
            onStopScan = {
                if (selectedRaspberryPi != null) {
                    val command = "SCAN_STOP"
                    bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                    println("🛑 发送停止扫描指令: $command")
                }
            }
        )
        
        // 扫描结果
        ScanResultsSection(
            scannedDevices = scannedDevices,
            onConnectDevice = { device ->
                if (selectedRaspberryPi != null) {
                    val command = "CONNECT:${device.address}"
                    bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                    println("🔗 发送连接指令: $command")
                }
            }
        )
        
        // 调试信息
        if (lastMessage.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
            ) {
                Text(
                    text = "最新消息: $lastMessage",
                    modifier = Modifier.padding(12.dp),
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 树莓派选择器
 */
@Composable
private fun RaspberryPiSelector(
    connectedDevices: List<BluetoothDeviceInfo>,
    selectedDevice: BluetoothDeviceInfo?,
    onDeviceSelected: (BluetoothDeviceInfo?) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "选择树莓派设备",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            val lubanDevices = connectedDevices.filter { 
                it.name?.contains("Luban") == true 
            }
            
            if (lubanDevices.isEmpty()) {
                Text(
                    text = "未找到Luban设备，请先连接树莓派",
                    color = Color.Red,
                    fontSize = 14.sp
                )
            } else {
                lubanDevices.forEach { device ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onDeviceSelected(device) }
                            .background(
                                if (selectedDevice?.address == device.address) 
                                    Color(0xFFE3F2FD) else Color.Transparent,
                                RoundedCornerShape(8.dp)
                            )
                            .padding(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedDevice?.address == device.address,
                            onClick = { onDeviceSelected(device) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                text = device.getDisplayName(),
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = device.address,
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 扫描设备数据类
 */
data class ScannedDevice(
    val type: String,
    val name: String,
    val address: String,
    val rssi: String = "未知"
)



/**
 * 扫描控制区域
 */
@Composable
private fun ScanControlSection(
    selectedRaspberryPi: BluetoothDeviceInfo?,
    isScanning: Boolean,
    scanStatus: String,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "扫描控制",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 状态显示
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                if (isScanning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(
                    text = scanStatus,
                    fontSize = 14.sp,
                    color = if (isScanning) Color(0xFF1976D2) else Color.Gray
                )
            }

            // 控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onStartScan,
                    enabled = !isScanning && selectedRaspberryPi != null,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Search, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("开始扫描")
                }

                Button(
                    onClick = onStopScan,
                    enabled = isScanning && selectedRaspberryPi != null,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(Icons.Default.Close, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("停止扫描")
                }
            }
        }
    }
}

/**
 * 扫描结果区域
 */
@Composable
private fun ScanResultsSection(
    scannedDevices: List<ScannedDevice>,
    onConnectDevice: (ScannedDevice) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "扫描结果 (${scannedDevices.size})",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            if (scannedDevices.isEmpty()) {
                Text(
                    text = "暂无扫描结果",
                    color = Color.Gray,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.heightIn(max = 300.dp)
                ) {
                    items(scannedDevices) { device ->
                        DeviceItem(
                            device = device,
                            onConnect = { onConnectDevice(device) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 设备项
 */
@Composable
private fun DeviceItem(
    device: ScannedDevice,
    onConnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 设备图标
            Icon(
                imageVector = getDeviceIcon(device.type),
                contentDescription = null,
                tint = getDeviceColor(device.type),
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 设备信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = device.name,
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
                Text(
                    text = "${getDeviceTypeName(device.type)} • ${device.address}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                if (device.rssi != "未知") {
                    Text(
                        text = "信号强度: ${device.rssi}",
                        fontSize = 11.sp,
                        color = Color.Gray
                    )
                }
            }

            // 连接按钮
            Button(
                onClick = onConnect,
                modifier = Modifier.height(32.dp),
                contentPadding = PaddingValues(horizontal = 12.dp)
            ) {
                Text("连接", fontSize = 12.sp)
            }
        }
    }
}

/**
 * 获取设备图标
 */
private fun getDeviceIcon(deviceType: String) = when (deviceType) {
    "01" -> Icons.Filled.CheckCircle // 蓝牙按键
    "02" -> Icons.Filled.CheckCircle // 充电器
    "03" -> Icons.Filled.CheckCircle // 主灯
    "04" -> Icons.Filled.CheckCircle // 氛围灯
    "05" -> Icons.Filled.CheckCircle // 香薰
    "06" -> Icons.Filled.CheckCircle // 风扇
    else -> Icons.Filled.CheckCircle
}

/**
 * 获取设备颜色
 */
private fun getDeviceColor(deviceType: String) = when (deviceType) {
    "01" -> Color(0xFF2196F3) // 蓝色
    "02" -> Color(0xFF4CAF50) // 绿色
    "03" -> Color(0xFFFF9800) // 橙色
    "04" -> Color(0xFF9C27B0) // 紫色
    "05" -> Color(0xFF00BCD4) // 青色
    "06" -> Color(0xFF607D8B) // 蓝灰色
    else -> Color.Gray
}

/**
 * 获取设备类型名称
 */
private fun getDeviceTypeName(deviceType: String) = when (deviceType) {
    "01" -> "蓝牙按键"
    "02" -> "充电器"
    "03" -> "主灯"
    "04" -> "氛围灯"
    "05" -> "香薰"
    "06" -> "风扇"
    else -> "未知设备"
}

/**
 * 解析设备列表
 */
private fun parseDeviceList(deviceListStr: String): List<ScannedDevice> {
    if (deviceListStr.isEmpty()) return emptyList()

    return deviceListStr.split(";").mapNotNull { deviceStr ->
        val parts = deviceStr.split("|")
        if (parts.size >= 3) {
            ScannedDevice(
                type = parts[0],
                name = parts[1],
                address = parts[2],
                rssi = if (parts.size >= 4) parts[3] else "未知"
            )
        } else null
    }
}
